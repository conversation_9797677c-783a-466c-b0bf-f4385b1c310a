/**
 * Test script to verify employee search functionality
 * Run this to debug search issues
 */

import { searchEmployeesByName, fetchEmployeeSearchItems } from '../lib/actions/search-employees'

async function testEmployeeSearch() {
  console.log('🧪 Testing Employee Search Functionality')
  console.log('=' .repeat(50))

  try {
    // Test 1: Fetch all employees (no search query)
    console.log('\n📋 Test 1: Fetching all employees...')
    const allEmployees = await fetchEmployeeSearchItems()
    console.log('✅ All employees count:', allEmployees.length)
    
    if (allEmployees.length > 0) {
      console.log('📋 Sample employees:')
      allEmployees.slice(0, 3).forEach(emp => {
        console.log(`  - ${emp.title} (${emp.href})`)
      })
    } else {
      console.log('❌ No employees found!')
    }

    // Test 2: Search for specific names
    const testQueries = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
    
    for (const query of testQueries) {
      console.log(`\n🔍 Test: Searching for "${query}"...`)
      const results = await searchEmployeesByName(query)
      console.log(`✅ Results for "${query}":`, results.length)
      
      if (results.length > 0) {
        results.forEach(result => {
          console.log(`  - ${result.title} (${result.href})`)
        })
      } else {
        console.log(`❌ No results found for "${query}"`)
      }
    }

    // Test 3: Fuzzy search test
    console.log('\n🔍 Test: Fuzzy search for "mon" (should find Mona)...')
    const fuzzyResults = await searchEmployeesByName('mon')
    console.log('✅ Fuzzy results:', fuzzyResults.length)
    
    if (fuzzyResults.length > 0) {
      fuzzyResults.forEach(result => {
        console.log(`  - ${result.title}`)
      })
    }

  } catch (error) {
    console.error('🚨 Test failed:', error)
  }

  console.log('\n🏁 Test completed!')
}

// Run the test if this file is executed directly
if (require.main === module) {
  testEmployeeSearch()
}

export { testEmployeeSearch }
