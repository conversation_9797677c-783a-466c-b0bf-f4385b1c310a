"use server"

import { getCurrentUser } from '../auth'
import { getEmployees, getEmployeesForManager } from '../data/employees'
import { supabaseAdmin } from '../supabase'
import type { SearchableItem } from '../search-data'
import type { Employee } from '../types'
import { debug } from '../debug'

/**
 * Fuzzy search employees directly from database using PostgreSQL similarity
 * This provides a fallback search method with fuzzy matching
 */
async function fuzzySearchEmployees(searchQuery: string, currentUserId: string, userRole: string): Promise<SearchableItem[]> {
  try {
    console.log('🔍 [FUZZY SEARCH] Starting fuzzy search for:', searchQuery)
    console.log('👤 [FUZZY SEARCH] User:', { id: currentUserId, role: userRole })

    // Enable pg_trgm extension for similarity search (if not already enabled)
    // This is a one-time setup that should be done in Supabase

    let query = supabaseAdmin
      .from('appy_employees')
      .select(`
        id,
        full_name,
        active,
        compensation,
        rate,
        department_id,
        manager_id,
        appy_departments:department_id (
          name
        )
      `)
      .eq('active', true)

    // Apply role-based filtering at database level
    if (userRole === 'manager') {
      // For managers, we could add a filter here, but for now let's get all and filter later
      // This is a fallback approach since hierarchical filtering might be complex
      console.log('🌳 [FUZZY SEARCH] Manager role - will filter hierarchically later')
    }

    // Add fuzzy search using ilike for now (can be enhanced with pg_trgm later)
    if (searchQuery.trim()) {
      query = query.ilike('full_name', `%${searchQuery}%`)
    }

    const { data: employees, error } = await query.limit(20)

    if (error) {
      console.error('🚨 [FUZZY SEARCH ERROR]:', error)
      return []
    }

    console.log('📊 [FUZZY SEARCH] Found employees:', employees?.length || 0)

    if (!employees || employees.length === 0) {
      console.log('📝 [FUZZY SEARCH] No employees found')
      return []
    }

    // Transform to searchable items
    const searchItems: SearchableItem[] = []

    employees.forEach(emp => {
      const departmentName = (emp.appy_departments as any)?.name || 'No Department'

      // Add appraisal page
      searchItems.push({
        id: `employee-appraisal-${emp.id}`,
        title: `${emp.full_name} - Appraisal`,
        subtitle: `${departmentName} • ${emp.rate === 'hourly' ? 'Hourly' : 'Monthly'}`,
        href: `/dashboard/appraisal/${emp.id}`,
        keywords: [
          emp.full_name.toLowerCase(),
          ...(emp.full_name.toLowerCase().split(' ')),
          departmentName.toLowerCase(),
          emp.rate || '',
          'employee',
          'appraisal',
          'review',
          'evaluation'
        ].filter(Boolean),
        icon: 'user',
        category: 'employee' as const
      })

      // Add profile page
      searchItems.push({
        id: `employee-profile-${emp.id}`,
        title: `${emp.full_name} - Profile`,
        subtitle: `View employee profile • ${departmentName}`,
        href: `/dashboard/employees/${emp.id}/profile`,
        keywords: [
          emp.full_name.toLowerCase(),
          ...(emp.full_name.toLowerCase().split(' ')),
          departmentName.toLowerCase(),
          'employee',
          'profile',
          'details',
          'information'
        ].filter(Boolean),
        icon: 'user',
        category: 'employee' as const
      })
    })

    console.log('✅ [FUZZY SEARCH] Generated search items:', searchItems.length)
    return searchItems

  } catch (error) {
    console.error('🚨 [FUZZY SEARCH ERROR]:', error)
    return []
  }
}

/**
 * Server action to fetch employees as searchable items with proper role-based filtering
 * This respects user permissions and hierarchical access patterns
 */
export async function fetchEmployeeSearchItems(searchQuery?: string): Promise<SearchableItem[]> {
  try {
    debug.log('🔍 [SEARCH DEBUG] Starting fetchEmployeeSearchItems with query:', searchQuery)

    // Get current user context for role-based filtering
    const currentUser = await getCurrentUser()

    if (!currentUser) {
      console.log('🚫 [SEARCH DEBUG] No authenticated user found')
      return []
    }

    debug.log('👤 [SEARCH DEBUG] Current user:', {
      id: currentUser.id,
      fullName: currentUser.fullName,
      role: currentUser.role
    })

    // If we have a search query, try fuzzy search first
    if (searchQuery && searchQuery.trim()) {
      console.log('🔍 [SEARCH DEBUG] Using fuzzy search for query:', searchQuery)
      const fuzzyResults = await fuzzySearchEmployees(searchQuery, currentUser.id, currentUser.role)
      if (fuzzyResults.length > 0) {
        console.log('✅ [SEARCH DEBUG] Fuzzy search returned results:', fuzzyResults.length)
        return fuzzyResults
      }
      console.log('📝 [SEARCH DEBUG] Fuzzy search returned no results, falling back to role-based search')
    }

    // Apply role-based filtering to get appropriate employees
    let filteredEmployees: Employee[] = []

    try {
      if (currentUser.role === 'super-admin') {
        // Super admins see all employees
        console.log('🔍 [SEARCH DEBUG] Fetching all employees for super-admin...')
        filteredEmployees = await getEmployees()
        console.log('🔍 [SEARCH DEBUG] Super admin sees all employees:', filteredEmployees.length)

        // Log first few employees for debugging
        if (filteredEmployees.length > 0) {
          console.log('📋 [SEARCH DEBUG] Sample employees:', filteredEmployees.slice(0, 3).map(emp => ({
            id: emp.id,
            fullName: emp.fullName,
            departmentName: emp.departmentName,
            active: emp.active
          })))
        }
      } else if (currentUser.role === 'accountant') {
        // Accountants see all employees (for payroll processing)
        filteredEmployees = await getEmployees()
        debug.log('🔍 [SEARCH DEBUG] Accountant sees all employees:', filteredEmployees.length)
      } else if (currentUser.role === 'manager') {
        // Managers see employees under their hierarchical supervision (including sub-managers)
        console.log('🌳 [SEARCH DEBUG] Fetching hierarchical employees for manager...')
        filteredEmployees = await getEmployeesForManager(currentUser.id)
        debug.log('🌳 [SEARCH DEBUG] Manager sees hierarchical employees:', filteredEmployees.length)
      } else if (currentUser.role === 'hr-admin' || currentUser.role === 'admin') {
        // HR admins and admins see all employees
        filteredEmployees = await getEmployees()
        debug.log('🔍 [SEARCH DEBUG] HR/Admin sees all employees:', filteredEmployees.length)
      } else {
        // Default: no access or employees only see themselves
        // For now, return empty array for unknown roles
        filteredEmployees = []
        debug.log('🚫 [SEARCH DEBUG] No access for role:', currentUser.role)
      }
    } catch (employeeError) {
      console.error('🚨 [SEARCH ERROR] Failed to fetch employees:', employeeError)
      // Fallback to fuzzy search if role-based filtering fails
      if (searchQuery && searchQuery.trim()) {
        console.log('🔄 [SEARCH DEBUG] Falling back to fuzzy search due to employee fetch error')
        return await fuzzySearchEmployees(searchQuery, currentUser.id, currentUser.role)
      }
      filteredEmployees = []
    }

    debug.log('📊 [SEARCH DEBUG] Filtered employees count:', filteredEmployees.length)

    // Transform employees to searchable items
    // We'll create two entries per employee: one for appraisal, one for profile
    const searchItems: SearchableItem[] = []

    filteredEmployees
      .filter(emp => emp.active) // Only include active employees
      .forEach(emp => {
        // Add appraisal page
        searchItems.push({
          id: `employee-appraisal-${emp.id}`,
          title: `${emp.fullName} - Appraisal`,
          subtitle: `${emp.departmentName || 'No Department'} • ${emp.compensation === 'hourly' ? 'Hourly' : 'Monthly'}`,
          href: `/dashboard/appraisal/${emp.id}`,
          keywords: [
            emp.fullName.toLowerCase(),
            ...(emp.fullName.toLowerCase().split(' ')), // Split name into parts
            emp.departmentName?.toLowerCase() || '',
            emp.compensation || '',
            'employee',
            'appraisal',
            'review',
            'evaluation'
          ].filter(Boolean),
          icon: 'user',
          category: 'employee' as const
        })

        // Add profile page
        searchItems.push({
          id: `employee-profile-${emp.id}`,
          title: `${emp.fullName} - Profile`,
          subtitle: `View employee profile • ${emp.departmentName || 'No Department'}`,
          href: `/dashboard/employees/${emp.id}/profile`,
          keywords: [
            emp.fullName.toLowerCase(),
            ...(emp.fullName.toLowerCase().split(' ')), // Split name into parts
            emp.departmentName?.toLowerCase() || '',
            'employee',
            'profile',
            'details',
            'information'
          ].filter(Boolean),
          icon: 'user',
          category: 'employee' as const
        })
      })

    debug.log('✅ [SEARCH DEBUG] Generated search items:', searchItems.length)

    // Log sample of generated items for debugging
    if (searchItems.length > 0) {
      debug.log('📋 [SEARCH DEBUG] Sample search items:', searchItems.slice(0, 2).map(item => ({
        id: item.id,
        title: item.title,
        subtitle: item.subtitle,
        href: item.href,
        category: item.category
      })))
    }

    return searchItems
  } catch (error) {
    console.error('🚨 [SEARCH ERROR] Failed to fetch employees for search:', error)
    console.error('🚨 [SEARCH ERROR] Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : 'Unknown'
    })

    // Return empty array on error to prevent search UI from breaking
    return []
  }
}

/**
 * Search employees by name with fuzzy matching
 * This is a simpler function that can be called directly with a search query
 */
export async function searchEmployeesByName(searchQuery: string): Promise<SearchableItem[]> {
  try {
    console.log('🔍 [EMPLOYEE SEARCH] Searching for:', searchQuery)

    if (!searchQuery || !searchQuery.trim()) {
      console.log('📝 [EMPLOYEE SEARCH] Empty query, returning all employees')
      return await fetchEmployeeSearchItems()
    }

    // Get current user for permissions
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      console.log('🚫 [EMPLOYEE SEARCH] No authenticated user')
      return []
    }

    // Use fuzzy search with the query
    const results = await fuzzySearchEmployees(searchQuery.trim(), currentUser.id, currentUser.role)
    console.log('✅ [EMPLOYEE SEARCH] Found results:', results.length)

    return results
  } catch (error) {
    console.error('🚨 [EMPLOYEE SEARCH ERROR]:', error)
    return []
  }
}