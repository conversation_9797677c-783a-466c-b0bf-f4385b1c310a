"use server"

import { db } from '../db'
import type { SearchableItem } from '../search-data'

/**
 * Server action to fetch employees as searchable items
 * This runs on the server where SUPABASE_SERVICE_ROLE_KEY is available
 */
export async function fetchEmployeeSearchItems(): Promise<SearchableItem[]> {
  try {
    const employees = await db.getEmployees()
    
    // Transform employees to searchable items
    // We'll create two entries per employee: one for appraisal, one for profile
    const searchItems: SearchableItem[] = []
    
    employees
      .filter(emp => emp.active) // Only include active employees
      .forEach(emp => {
        // Add appraisal page
        searchItems.push({
          id: `employee-appraisal-${emp.id}`,
          title: `${emp.full_name} - Appraisal`,
          subtitle: `${emp.department_name || 'No Department'} • ${emp.rate === 'hourly' ? 'Hourly' : 'Monthly'}`,
          href: `/dashboard/appraisal/${emp.id}`,
          keywords: [
            emp.full_name.toLowerCase(),
            ...(emp.full_name.toLowerCase().split(' ')), // Split name into parts
            emp.department_name?.toLowerCase() || '',
            emp.rate || '',
            'employee',
            'appraisal',
            'review',
            'evaluation'
          ].filter(Boolean),
          icon: 'user',
          category: 'employee' as const
        })
        
        // Add profile page
        searchItems.push({
          id: `employee-profile-${emp.id}`,
          title: `${emp.full_name} - Profile`,
          subtitle: `View employee profile • ${emp.department_name || 'No Department'}`,
          href: `/dashboard/employees/${emp.id}/profile`,
          keywords: [
            emp.full_name.toLowerCase(),
            ...(emp.full_name.toLowerCase().split(' ')), // Split name into parts
            emp.department_name?.toLowerCase() || '',
            'employee',
            'profile',
            'details',
            'information'
          ].filter(Boolean),
          icon: 'user',
          category: 'employee' as const
        })
      })
    
    return searchItems
  } catch (error) {
    console.error('Failed to fetch employees for search:', error)
    return []
  }
}