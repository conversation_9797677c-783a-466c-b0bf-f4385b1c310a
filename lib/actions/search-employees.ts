"use server"

import { getCurrentUser } from '../auth'
import { getEmployees, getEmployeesForManager } from '../data/employees'
import type { SearchableItem } from '../search-data'
import type { Employee } from '../types'
import { debug } from '../debug'

/**
 * Server action to fetch employees as searchable items with proper role-based filtering
 * This respects user permissions and hierarchical access patterns
 */
export async function fetchEmployeeSearchItems(): Promise<SearchableItem[]> {
  try {
    debug.log('🔍 [SEARCH DEBUG] Starting fetchEmployeeSearchItems')

    // Get current user context for role-based filtering
    const currentUser = await getCurrentUser()

    if (!currentUser) {
      console.log('🚫 [SEARCH DEBUG] No authenticated user found')
      return []
    }

    debug.log('👤 [SEARCH DEBUG] Current user:', {
      id: currentUser.id,
      fullName: currentUser.fullName,
      role: currentUser.role
    })

    // Apply role-based filtering to get appropriate employees
    let filteredEmployees: Employee[] = []

    if (currentUser.role === 'super-admin') {
      // Super admins see all employees
      filteredEmployees = await getEmployees()
      debug.log('🔍 [SEARCH DEBUG] Super admin sees all employees:', filteredEmployees.length)
    } else if (currentUser.role === 'accountant') {
      // Accountants see all employees (for payroll processing)
      filteredEmployees = await getEmployees()
      debug.log('🔍 [SEARCH DEBUG] Accountant sees all employees:', filteredEmployees.length)
    } else if (currentUser.role === 'manager') {
      // Managers see employees under their hierarchical supervision (including sub-managers)
      filteredEmployees = await getEmployeesForManager(currentUser.id)
      debug.log('🌳 [SEARCH DEBUG] Manager sees hierarchical employees:', filteredEmployees.length)
    } else if (currentUser.role === 'hr-admin' || currentUser.role === 'admin') {
      // HR admins and admins see all employees
      filteredEmployees = await getEmployees()
      debug.log('🔍 [SEARCH DEBUG] HR/Admin sees all employees:', filteredEmployees.length)
    } else {
      // Default: no access or employees only see themselves
      // For now, return empty array for unknown roles
      filteredEmployees = []
      debug.log('🚫 [SEARCH DEBUG] No access for role:', currentUser.role)
    }

    debug.log('📊 [SEARCH DEBUG] Filtered employees count:', filteredEmployees.length)

    // Transform employees to searchable items
    // We'll create two entries per employee: one for appraisal, one for profile
    const searchItems: SearchableItem[] = []

    filteredEmployees
      .filter(emp => emp.active) // Only include active employees
      .forEach(emp => {
        // Add appraisal page
        searchItems.push({
          id: `employee-appraisal-${emp.id}`,
          title: `${emp.fullName} - Appraisal`,
          subtitle: `${emp.departmentName || 'No Department'} • ${emp.compensation === 'hourly' ? 'Hourly' : 'Monthly'}`,
          href: `/dashboard/appraisal/${emp.id}`,
          keywords: [
            emp.fullName.toLowerCase(),
            ...(emp.fullName.toLowerCase().split(' ')), // Split name into parts
            emp.departmentName?.toLowerCase() || '',
            emp.compensation || '',
            'employee',
            'appraisal',
            'review',
            'evaluation'
          ].filter(Boolean),
          icon: 'user',
          category: 'employee' as const
        })

        // Add profile page
        searchItems.push({
          id: `employee-profile-${emp.id}`,
          title: `${emp.fullName} - Profile`,
          subtitle: `View employee profile • ${emp.departmentName || 'No Department'}`,
          href: `/dashboard/employees/${emp.id}/profile`,
          keywords: [
            emp.fullName.toLowerCase(),
            ...(emp.fullName.toLowerCase().split(' ')), // Split name into parts
            emp.departmentName?.toLowerCase() || '',
            'employee',
            'profile',
            'details',
            'information'
          ].filter(Boolean),
          icon: 'user',
          category: 'employee' as const
        })
      })

    debug.log('✅ [SEARCH DEBUG] Generated search items:', searchItems.length)
    return searchItems
  } catch (error) {
    console.error('🚨 [SEARCH ERROR] Failed to fetch employees for search:', error)
    return []
  }
}