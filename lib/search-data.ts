/**
 * Static search data for the command palette
 * This replaces the complex database search with simple client-side filtering
 */

export type SearchableItem = {
  id: string
  title: string
  subtitle?: string
  href: string
  keywords?: string[]
  icon: string
  category: 'navigation' | 'action' | 'recent' | 'employee'
}

export const searchableItems: SearchableItem[] = [
  // Navigation items
  {
    id: 'nav-dashboard',
    title: 'Dashboard',
    subtitle: 'View overview and statistics',
    href: '/dashboard',
    keywords: ['home', 'overview', 'stats', 'main'],
    icon: 'home',
    category: 'navigation'
  },
  {
    id: 'nav-employees',
    title: 'Employees',
    subtitle: 'Manage all employees',
    href: '/dashboard/employees',
    keywords: ['staff', 'people', 'workers', 'team'],
    icon: 'users',
    category: 'navigation'
  },
  {
    id: 'nav-departments',
    title: 'Departments',
    subtitle: 'View and manage departments',
    href: '/dashboard/departments',
    keywords: ['teams', 'divisions', 'units'],
    icon: 'building',
    category: 'navigation'
  },
  {
    id: 'nav-periods',
    title: 'Appraisal Periods',
    subtitle: 'Manage appraisal periods',
    href: '/dashboard/periods',
    keywords: ['review', 'evaluation', 'assessment'],
    icon: 'calendar',
    category: 'navigation'
  },
  {
    id: 'nav-approvals',
    title: 'Approvals',
    subtitle: 'Review and approve appraisals',
    href: '/dashboard/approvals',
    keywords: ['review', 'approve', 'pending'],
    icon: 'check-circle',
    category: 'navigation'
  },
  {
    id: 'nav-approvals-payment',
    title: 'Employees approved for payment',
    subtitle: 'View employees ready for payment',
    href: '/dashboard/approvals?status=ready-to-pay',
    keywords: ['payment', 'approved', 'ready', 'pay', 'salary', 'compensation'],
    icon: 'dollar-sign',
    category: 'navigation'
  },
  {
    id: 'nav-pto',
    title: 'PTO Management',
    subtitle: 'Manage time off requests',
    href: '/dashboard/pto',
    keywords: ['leave', 'vacation', 'time off', 'holiday'],
    icon: 'calendar-off',
    category: 'navigation'
  },
  {
    id: 'nav-team',
    title: 'Team View',
    subtitle: 'View your team members',
    href: '/dashboard/team',
    keywords: ['my team', 'direct reports', 'subordinates'],
    icon: 'users-2',
    category: 'navigation'
  },
  {
    id: 'nav-admin',
    title: 'Admin Panel',
    subtitle: 'System administration',
    href: '/dashboard/admin',
    keywords: ['settings', 'configuration', 'management'],
    icon: 'settings',
    category: 'navigation'
  },

  // Common actions
  {
    id: 'action-create-employee',
    title: 'Create New Employee',
    subtitle: 'Add a new employee to the system',
    href: '/dashboard/employees?action=create',
    keywords: ['add', 'new', 'employee', 'hire'],
    icon: 'user-plus',
    category: 'action'
  },
  {
    id: 'action-create-department',
    title: 'Create New Department',
    subtitle: 'Add a new department',
    href: '/dashboard/departments?action=create',
    keywords: ['add', 'new', 'department', 'division'],
    icon: 'plus-circle',
    category: 'action'
  },
  {
    id: 'action-create-period',
    title: 'Create Appraisal Period',
    subtitle: 'Start a new appraisal period',
    href: '/dashboard/periods?action=create',
    keywords: ['add', 'new', 'period', 'review', 'cycle'],
    icon: 'calendar-plus',
    category: 'action'
  },
  {
    id: 'action-view-pending',
    title: 'View Pending Appraisals',
    subtitle: 'See appraisals awaiting review',
    href: '/dashboard/approvals?status=pending',
    keywords: ['pending', 'waiting', 'review', 'unapproved'],
    icon: 'clock',
    category: 'action'
  },
  {
    id: 'action-view-submitted',
    title: 'View Submitted Appraisals',
    subtitle: 'See completed appraisals',
    href: '/dashboard/approvals?status=submitted',
    keywords: ['submitted', 'completed', 'done', 'finished'],
    icon: 'check',
    category: 'action'
  },
  {
    id: 'action-contact-manager',
    title: 'Employees to Contact Manager',
    subtitle: 'View employees needing manager contact',
    href: '/dashboard/approvals?status=contact-manager',
    keywords: ['contact', 'manager', 'issue', 'problem'],
    icon: 'alert-circle',
    category: 'action'
  },

  // Quick filters
  {
    id: 'filter-hourly-employees',
    title: 'Hourly Employees',
    subtitle: 'View all hourly employees',
    href: '/dashboard/employees?rate=hourly',
    keywords: ['hourly', 'wage', 'contract'],
    icon: 'clock',
    category: 'action'
  },
  {
    id: 'filter-monthly-employees',
    title: 'Monthly Employees',
    subtitle: 'View all salaried employees',
    href: '/dashboard/employees?rate=monthly',
    keywords: ['monthly', 'salary', 'salaried'],
    icon: 'calendar',
    category: 'action'
  },
  {
    id: 'filter-active-periods',
    title: 'Active Appraisal Periods',
    subtitle: 'View current appraisal periods',
    href: '/dashboard/periods?status=active',
    keywords: ['active', 'current', 'ongoing'],
    icon: 'activity',
    category: 'action'
  }
]

// Function to search items with simple filtering
export function searchItems(query: string): SearchableItem[] {
  if (!query.trim()) {
    return searchableItems
  }

  const normalizedQuery = query.toLowerCase().trim()
  
  return searchableItems.filter(item => {
    // Check title
    if (item.title.toLowerCase().includes(normalizedQuery)) {
      return true
    }
    
    // Check subtitle
    if (item.subtitle && item.subtitle.toLowerCase().includes(normalizedQuery)) {
      return true
    }
    
    // Check keywords
    if (item.keywords) {
      return item.keywords.some(keyword => 
        keyword.toLowerCase().includes(normalizedQuery)
      )
    }
    
    return false
  })
}

// Get recent items (could be stored in localStorage)
export function getRecentItems(): SearchableItem[] {
  // For now, return some default recent items
  return [
    searchableItems.find(item => item.id === 'nav-approvals-payment'),
    searchableItems.find(item => item.id === 'nav-employees'),
    searchableItems.find(item => item.id === 'action-view-pending')
  ].filter(Boolean) as SearchableItem[]
}

// Search function for filtering items
export function filterSearchItems(items: SearchableItem[], query: string): SearchableItem[] {
  if (!query.trim()) {
    return items
  }

  const normalizedQuery = query.toLowerCase().trim()
  
  return items.filter(item => {
    // Check title
    if (item.title.toLowerCase().includes(normalizedQuery)) {
      return true
    }
    
    // Check subtitle
    if (item.subtitle && item.subtitle.toLowerCase().includes(normalizedQuery)) {
      return true
    }
    
    // Check keywords
    if (item.keywords) {
      return item.keywords.some(keyword => 
        keyword.toLowerCase().includes(normalizedQuery)
      )
    }
    
    return false
  })
}