"use client"

import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import { searchItems, filterSearchItems, getRecentItems, type SearchableItem } from "@/lib/search-data"
import { fetchEmployeeSearchItems, searchEmployeesByName } from "@/lib/actions/search-employees"

export type SearchResult = SearchableItem & {
  type: string // For compatibility with existing component
}

export function useGlobalSearch() {
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState("")
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [recentSearches, setRecentSearches] = useState<SearchResult[]>([])
  const [hasSearched, setHasSearched] = useState(false)
  const router = useRouter()

  // Load recent searches on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem("global-search-recent")
      if (stored) {
        setRecentSearches(JSON.parse(stored))
      } else {
        // Use default recent items
        const recentItems = getRecentItems().map(item => ({
          ...item,
          type: item.category
        }))
        setRecentSearches(recentItems)
      }
    } catch (error) {
      console.error("Failed to load recent searches:", error)
      // Use default recent items as fallback
      const recentItems = getRecentItems().map(item => ({
        ...item,
        type: item.category
      }))
      setRecentSearches(recentItems)
    }
  }, [])

  // Debug effect to track results changes
  useEffect(() => {
    console.log('🔄 [HOOK DEBUG] Results state changed:', {
      length: results.length,
      sample: results.slice(0, 2),
      query,
      loading,
      hasSearched
    })
  }, [results, query, loading, hasSearched])

  // Manual search function
  const performSearch = useCallback(async () => {
    console.log('🔍 [HOOK DEBUG] performSearch - Starting search with query:', query)

    if (!query.trim()) {
      console.log('📝 [HOOK DEBUG] performSearch - Empty query, showing recent searches')
      setResults(recentSearches.slice(0, 5))
      setHasSearched(false)
      return
    }

    setLoading(true)
    setHasSearched(true)

    try {
      console.log('🔎 [HOOK DEBUG] performSearch - Searching static items...')
      // Search static items
      const staticResults = searchItems(query)
      console.log('✅ [HOOK DEBUG] performSearch - Found', staticResults.length, 'static results')

      console.log('👥 [HOOK DEBUG] performSearch - Calling searchEmployeesByName...')
      // Search employees directly with the query for better fuzzy matching
      const employeeResults = await searchEmployeesByName(query)
      console.log('✅ [HOOK DEBUG] performSearch - Received', employeeResults.length, 'employee results')
      console.log('📋 [HOOK DEBUG] performSearch - Employee results sample:', employeeResults.slice(0, 2))

      // Combine results
      const allResults = [...staticResults, ...employeeResults]
      console.log('🎯 [HOOK DEBUG] performSearch - Total combined results:', allResults.length)

      // Transform results - ensure each result has the required properties
      const transformedResults = allResults.map(item => {
        const transformed = {
          ...item,
          type: item.category || item.type || 'navigation',
          // Ensure all required properties exist
          id: item.id || `fallback-${Math.random()}`,
          title: item.title || 'Untitled',
          href: item.href || '#',
          icon: item.icon || 'navigation'
        }
        return transformed
      })

      console.log('🔄 [HOOK DEBUG] performSearch - Transformed results:', transformedResults.length)
      console.log('📋 [HOOK DEBUG] performSearch - Sample transformed result:', transformedResults[0])

      // Force state update
      setResults(transformedResults)

      // Double-check state was set
      setTimeout(() => {
        console.log('⏰ [HOOK DEBUG] performSearch - State check after 100ms')
      }, 100)

    } catch (error) {
      console.error('🚨 [HOOK ERROR] performSearch - Search error:', error)
      setResults([])
    } finally {
      setLoading(false)
      console.log('✅ [HOOK DEBUG] performSearch - Search completed')
    }
  }, [query, recentSearches])

  // Show recent searches when no search has been performed
  useEffect(() => {
    if (!hasSearched && !query.trim()) {
      setResults(recentSearches.slice(0, 5))
    }
  }, [recentSearches, hasSearched, query])

  // Save recent searches to localStorage
  const saveRecentSearch = useCallback((result: SearchResult) => {
    setRecentSearches(prev => {
      const filtered = prev.filter(item => item.id !== result.id)
      const updated = [result, ...filtered].slice(0, 10) // Keep last 10
      
      try {
        localStorage.setItem("global-search-recent", JSON.stringify(updated))
      } catch (error) {
        console.error("Failed to save recent search:", error)
      }
      
      return updated
    })
  }, [])

  // Handle result selection
  const selectResult = useCallback((result: SearchResult) => {
    saveRecentSearch(result)
    setIsOpen(false)
    setQuery("")
    router.push(result.href)
  }, [router, saveRecentSearch])

  // Keyboard shortcut handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "k" && (event.metaKey || event.ctrlKey)) {
        event.preventDefault()
        setIsOpen(prev => !prev)
      }
      
      if (event.key === "Escape" && isOpen) {
        event.preventDefault()
        setIsOpen(false)
        setQuery("")
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [isOpen])

  // Reset when closing
  useEffect(() => {
    if (!isOpen) {
      setQuery("")
      setResults(recentSearches.slice(0, 5))
      setHasSearched(false)
      setLoading(false)
    }
  }, [isOpen, recentSearches])

  return {
    isOpen,
    setIsOpen,
    query,
    setQuery,
    results,
    loading,
    selectResult,
    performSearch,
    showingRecent: !hasSearched && !query.trim(),
  }
}