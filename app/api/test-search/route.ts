import { NextRequest, NextResponse } from 'next/server'
import { searchEmployeesByName, fetchEmployeeSearchItems } from '@/lib/actions/search-employees'
import { getCurrentUser } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''
    
    console.log('🧪 [TEST API] Testing search with query:', query)
    
    // Get current user info
    const currentUser = await getCurrentUser()
    console.log('👤 [TEST API] Current user:', {
      id: currentUser?.id,
      fullName: currentUser?.fullName,
      role: currentUser?.role
    })

    let results
    if (query.trim()) {
      console.log('🔍 [TEST API] Searching with query:', query)
      results = await searchEmployeesByName(query)
    } else {
      console.log('📋 [TEST API] Fetching all employees')
      results = await fetchEmployeeSearchItems()
    }

    console.log('✅ [TEST API] Results count:', results.length)

    return NextResponse.json({
      success: true,
      query,
      currentUser: currentUser ? {
        id: currentUser.id,
        fullName: currentUser.fullName,
        role: currentUser.role
      } : null,
      resultsCount: results.length,
      results: results.slice(0, 10), // Limit to first 10 for testing
      message: `Found ${results.length} results${query ? ` for "${query}"` : ''}`
    })

  } catch (error) {
    console.error('🚨 [TEST API ERROR]:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Search test failed'
    }, { status: 500 })
  }
}
