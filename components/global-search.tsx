"use client"

import React from "react"
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { useGlobalSearch } from "@/hooks/use-global-search"
import {
  User,
  Building2,
  UserCheck,
  Calendar,
  Navigation,
  CalendarDays,
  Search,
  Clock,
  Loader2,
  Home,
  Users,
  Building,
  CheckCircle,
  DollarSign,
  CalendarOff,
  Users2,
  Settings,
  UserPlus,
  PlusCircle,
  CalendarPlus,
  Check,
  AlertCircle,
  Activity,
} from "lucide-react"
import { Button } from "@/components/ui/button"

const iconMap: Record<string, any> = {
  home: Home,
  users: Users,
  building: Building,
  calendar: Calendar,
  'check-circle': CheckCircle,
  'dollar-sign': DollarSign,
  'calendar-off': CalendarOff,
  'users-2': Users2,
  settings: Settings,
  'user-plus': UserPlus,
  'plus-circle': PlusCircle,
  'calendar-plus': CalendarPlus,
  clock: Clock,
  check: Check,
  'alert-circle': Al<PERSON><PERSON>ircle,
  activity: Activity,
  user: User,
  building2: Building2,
  'user-check': UserCheck,
  navigation: Navigation,
  'calendar-days': CalendarDays,
}

const typeLabels = {
  navigation: "Page",
  action: "Action",
  recent: "Recent",
  employee: "Employee",
  department: "Department",
  manager: "Manager",
  period: "Appraisal Period",
  pto: "PTO Request",
}

const typeColors = {
  navigation: "bg-blue-100 text-blue-800",
  action: "bg-green-100 text-green-800",
  recent: "bg-gray-100 text-gray-800",
  employee: "bg-purple-100 text-purple-800",
  department: "bg-orange-100 text-orange-800",
  manager: "bg-indigo-100 text-indigo-800",
  period: "bg-yellow-100 text-yellow-800",
  pto: "bg-pink-100 text-pink-800",
}

export function GlobalSearch() {
  const {
    isOpen,
    setIsOpen,
    query,
    setQuery,
    results,
    loading,
    selectResult,
    performSearch,
    showingRecent,
  } = useGlobalSearch()

  // Group results by type
  const groupedResults = React.useMemo(() => {
    try {
      if (!Array.isArray(results)) {
        return {}
      }

      return results.reduce((acc, result) => {
        if (!result || typeof result !== 'object') {
          return acc
        }

        const type = result.type || 'navigation'
        if (!acc[type]) {
          acc[type] = []
        }
        acc[type].push(result)
        return acc
      }, {} as Record<string, typeof results>)
    } catch (error) {
      console.error('Error grouping results:', error)
      return {}
    }
  }, [results])

  const handleSelect = (result: any) => {
    try {
      if (!result) {
        return
      }
      selectResult(result)
    } catch (error) {
      console.error('Error selecting result:', error)
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      console.log('⌨️ [DEBUG] handleKeyDown - Enter key pressed, triggering search')
      performSearch()
    }
  }

  const handleSearchClick = () => {
    console.log('🖱️ [DEBUG] handleSearchClick - Search button clicked')
    performSearch()
  }

  return (
    <CommandDialog open={isOpen} onOpenChange={setIsOpen}>
      <div className="flex items-center border-b px-3">
        <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
        <CommandInput
          placeholder="Type to search pages, actions, or employees... (Press Enter to search)"
          value={query}
          onValueChange={setQuery}
          onKeyDown={handleKeyDown}
          className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 border-0"
        />
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSearchClick}
          disabled={!query.trim() || loading}
          className="ml-2 h-8 px-2"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            "Search"
          )}
        </Button>
      </div>
      {loading && (
        <div className="flex items-center justify-center px-3 py-2">
          <Loader2 className="h-4 w-4 animate-spin opacity-50" />
        </div>
      )}

      <CommandList className="max-h-[400px] overflow-y-auto">
        {results.length === 0 && !loading && !showingRecent && (
          <CommandEmpty>
            {query.trim() ? "No results found. Try a different search term." : "Type your search query and press Enter or click Search."}
          </CommandEmpty>
        )}

        {showingRecent && results.length > 0 && (
          <CommandGroup heading="Recent">
            {results.map((result) => {
              const IconComponent = iconMap[result.icon as keyof typeof iconMap] || Navigation
              const type = result.type || 'navigation'
              return (
                <CommandItem
                  key={result.id}
                  value={result.id}
                  onSelect={() => handleSelect(result)}
                  className="flex items-center gap-3 px-3 py-2"
                >
                  <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted">
                    <IconComponent className="h-4 w-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium truncate">{result.title}</span>
                      <Badge
                        variant="secondary"
                        className={`text-xs ${typeColors[type as keyof typeof typeColors] || typeColors.navigation}`}
                      >
                        {typeLabels[type as keyof typeof typeLabels] || type}
                      </Badge>
                    </div>
                    {result.subtitle && (
                      <p className="text-sm text-muted-foreground truncate">
                        {result.subtitle}
                      </p>
                    )}
                  </div>
                  <Clock className="h-3 w-3 text-muted-foreground" />
                </CommandItem>
              )
            })}
          </CommandGroup>
        )}

        {!showingRecent && (
          <>
            {Object.entries(groupedResults).map(([type, typeResults], index) => (
              <div key={type}>
                {index > 0 && <CommandSeparator />}
                <CommandGroup heading={typeLabels[type as keyof typeof typeLabels] || type}>
                  {typeResults.map((result) => {
                    if (!result || !result.id) {
                      return null
                    }

                    const IconComponent = iconMap[result.icon as keyof typeof iconMap] || Navigation
                    return (
                      <CommandItem
                        key={result.id}
                        value={result.id}
                        onSelect={() => handleSelect(result)}
                        className="flex items-center gap-3 px-3 py-2"
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted">
                          <IconComponent className="h-4 w-4" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate">{result.title || 'Untitled'}</span>
                            <Badge
                              variant="secondary"
                              className={`text-xs ${typeColors[type as keyof typeof typeColors] || typeColors.navigation}`}
                            >
                              {typeLabels[type as keyof typeof typeLabels] || type}
                            </Badge>
                          </div>
                          {result.subtitle && (
                            <p className="text-sm text-muted-foreground truncate">
                              {result.subtitle}
                            </p>
                          )}
                        </div>
                      </CommandItem>
                    )
                  })}
                </CommandGroup>
              </div>
            ))}
          </>
        )}

        {results.length > 0 && (
          <>
            <CommandSeparator />
            <div className="px-3 py-2 text-xs text-muted-foreground">
              {!showingRecent && (
                <>
                  <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                    <span className="text-xs">⏎</span>
                  </kbd>{" "}
                  to select •{" "}
                  <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                    <span className="text-xs">↑↓</span>
                  </kbd>{" "}
                  to navigate •{" "}
                </>
              )}
              <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                <span className="text-xs">ESC</span>
              </kbd>{" "}
              to close
              {!showingRecent && (
                <>
                  {" "}•{" "}
                  <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                    <span className="text-xs">⏎</span>
                  </kbd>{" "}
                  to search
                </>
              )}
            </div>
          </>
        )}
      </CommandList>
    </CommandDialog>
  )
}