"use client"

import { useState } from 'react'
import { searchEmployeesByName } from '@/lib/actions/search-employees'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { User } from 'lucide-react'

interface SearchResult {
  id: string
  title: string
  subtitle?: string
  href: string
}

interface SimpleSearchProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SimpleSearch({ open, onOpenChange }: SimpleSearchProps) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)

  const handleSearch = async () => {
    if (!query.trim()) return
    
    setLoading(true)
    console.log('🔍 [SIMPLE SEARCH] Searching for:', query)
    
    try {
      const searchResults = await searchEmployeesByName(query)
      console.log('✅ [SIMPLE SEARCH] Got results:', searchResults)
      setResults(searchResults)
    } catch (error) {
      console.error('🚨 [SIMPLE SEARCH] Error:', error)
      setResults([])
    } finally {
      setLoading(false)
    }
  }

  const handleResultClick = (result: SearchResult) => {
    window.location.href = result.href
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Simple Employee Search</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search for employees..."
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              className="flex-1"
            />
            <Button 
              onClick={handleSearch} 
              disabled={loading || !query.trim()}
            >
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </div>

          <div className="text-sm text-gray-600">
            Results: {results.length} found
          </div>

          <div className="max-h-96 overflow-y-auto space-y-2">
            {results.map((result) => (
              <div
                key={result.id}
                onClick={() => handleResultClick(result)}
                className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer flex items-center gap-3"
              >
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">{result.title}</div>
                  {result.subtitle && (
                    <div className="text-sm text-gray-600">{result.subtitle}</div>
                  )}
                  <div className="text-xs text-blue-600">{result.href}</div>
                </div>
              </div>
            ))}
          </div>

          {results.length === 0 && !loading && query && (
            <div className="text-center text-gray-500 py-8">
              No results found for "{query}"
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
