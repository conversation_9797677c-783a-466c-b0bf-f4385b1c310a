"use client"

import { useState } from 'react'
import { searchEmployeesByName } from '@/lib/actions/search-employees'

export function SearchTest() {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  const handleSearch = async () => {
    if (!query.trim()) return
    
    setLoading(true)
    console.log('🧪 [TEST] Starting search for:', query)
    
    try {
      const searchResults = await searchEmployeesByName(query)
      console.log('🧪 [TEST] Received results:', searchResults)
      setResults(searchResults)
    } catch (error) {
      console.error('🧪 [TEST] Search error:', error)
      setResults([])
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Search Test Component</h3>
      
      <div className="flex gap-2 mb-4">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search for employees..."
          className="flex-1 px-3 py-2 border rounded"
          onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
        />
        <button
          onClick={handleSearch}
          disabled={loading || !query.trim()}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {loading ? 'Searching...' : 'Search'}
        </button>
      </div>

      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Results: {results.length} found
        </p>
        
        {results.length > 0 && (
          <div className="space-y-2">
            {results.map((result, index) => (
              <div key={result.id || index} className="p-2 bg-white border rounded">
                <div className="font-medium">{result.title}</div>
                <div className="text-sm text-gray-600">{result.subtitle}</div>
                <div className="text-xs text-blue-600">{result.href}</div>
              </div>
            ))}
          </div>
        )}
        
        {results.length === 0 && !loading && query && (
          <p className="text-sm text-gray-500">No results found</p>
        )}
      </div>
    </div>
  )
}
